#!/usr/bin/env python3
"""
InfoBlox MCP Backend Server
Connects the web interface to the actual InfoBlox MCP server
"""

import json
import sys
import os
import asyncio
from flask import Flask, request, jsonify
from flask_cors import CORS

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Import the actual MCP server components
try:
    from infoblox_mcp.tools import ToolRegistry
    from infoblox_mcp.client import InfoBloxClient
    from infoblox_mcp.config import InfoBloxConfig
    print("✅ Successfully imported InfoBlox MCP components")
except ImportError as e:
    print(f"❌ Error importing MCP components: {e}")
    ToolRegistry = None
    InfoBloxClient = None
    InfoBloxConfig = None

app = Flask(__name__)
CORS(app)

class InfoBloxMCPClient:
    def __init__(self):
        """Initialize the real InfoBlox MCP client."""
        self.tool_registry = None
        self.infoblox_client = None
        self._initialize_infoblox_connection()

    def _initialize_infoblox_connection(self):
        """Initialize connection to InfoBlox."""
        try:
            if not InfoBloxConfig or not InfoBloxClient or not ToolRegistry:
                print("❌ InfoBlox MCP components not available")
                return

            # Create configuration from environment variables or defaults
            grid_master_ip = os.environ.get('INFOBLOX_HOST', '************')
            username = os.environ.get('INFOBLOX_USERNAME', 'admin')
            password = os.environ.get('INFOBLOX_PASSWORD', 'infoblox')

            print(f"🔗 Connecting to InfoBlox at {grid_master_ip} as {username}")

            config = InfoBloxConfig(
                grid_master_ip=grid_master_ip,
                username=username,
                password=password,
                wapi_version="v2.12.3",  # Correct WAPI version
                verify_ssl=False  # Disable SSL verification for testing
            )

            # Initialize InfoBlox client with timeout
            print("🔄 Initializing InfoBlox client...")
            self.infoblox_client = InfoBloxClient(config)

            # Test connection with timeout
            print("🔄 Testing InfoBlox connection...")
            if self.infoblox_client.test_connection():
                print("✅ Successfully connected to InfoBlox")

                # Initialize tool registry
                self.tool_registry = ToolRegistry()
                print("✅ InfoBlox tools initialized")
            else:
                print("❌ Failed to connect to InfoBlox - will return error messages")

        except Exception as e:
            print(f"❌ Error initializing InfoBlox connection: {e}")
            print("⚠️  Backend will start without InfoBlox connection")
            self.infoblox_client = None
            self.tool_registry = None

    def call_mcp_server(self, tool_name, arguments=None):
        """Call the actual InfoBlox MCP tools."""
        try:
            if not self.tool_registry or not self.infoblox_client:
                return {"error": "InfoBlox connection not available. Please check configuration."}

            # Map simplified tool names to actual MCP tool names
            tool_name_mapping = {
                'get_zones': 'infoblox_dns_list_zones',
                'get_networks': 'infoblox_dhcp_list_networks',
                'get_grid_members': 'infoblox_grid_list_members',
                'get_system_health': 'infoblox_grid_get_status',
                'get_host_records': 'infoblox_dns_search_records',
                'get_a_records': 'infoblox_dns_search_records',
                'get_network_utilization': 'infoblox_ipam_get_network_utilization',
                'get_dhcp_leases': 'infoblox_dhcp_list_networks',
                'get_discovery_status': 'infoblox_grid_get_status',
                'get_dns_statistics': 'infoblox_grid_get_status',
                'get_dhcp_statistics': 'infoblox_grid_get_status',
                'get_system_info': 'infoblox_grid_get_status'
            }

            actual_tool_name = tool_name_mapping.get(tool_name)
            if not actual_tool_name:
                return {"error": f"Unknown tool: {tool_name}"}

            # Prepare arguments for specific tools
            tool_arguments = arguments or {}

            # Special handling for A records
            if tool_name == 'get_a_records':
                tool_arguments = {"record_type": "A"}
                if arguments and 'zone' in arguments:
                    tool_arguments['zone'] = arguments['zone']
            elif tool_name == 'get_host_records':
                tool_arguments = {"record_type": "A"}

            # Execute the tool using asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(
                    self.tool_registry.execute_tool(actual_tool_name, tool_arguments, self.infoblox_client)
                )
                # Parse JSON result if it's a string
                if isinstance(result, str):
                    try:
                        return json.loads(result)
                    except json.JSONDecodeError:
                        return {"message": result}
                return result
            finally:
                loop.close()

        except Exception as e:
            return {"error": f"Failed to call InfoBlox tool {tool_name}: {str(e)}"}

mcp_client = InfoBloxMCPClient()

@app.route('/api/query', methods=['POST'])
def handle_query():
    """Handle InfoBlox queries from the web interface"""
    try:
        data = request.get_json()
        query = data.get('query', '').lower()
        
        # Map natural language queries to MCP tools
        if 'dns zone' in query or 'zones' in query:
            result = mcp_client.call_mcp_server('get_zones')

        elif 'dhcp network' in query or 'networks' in query:
            result = mcp_client.call_mcp_server('get_networks')

        elif 'grid member' in query or 'members' in query:
            result = mcp_client.call_mcp_server('get_grid_members')

        elif 'host record' in query or 'hosts' in query:
            result = mcp_client.call_mcp_server('get_host_records')

        elif 'a record' in query and 'example.com' in query:
            result = mcp_client.call_mcp_server('get_a_records', {'zone': 'example.com'})

        elif 'utilization' in query or 'usage' in query:
            result = mcp_client.call_mcp_server('get_network_utilization')

        elif 'lease' in query or 'dhcp lease' in query:
            result = mcp_client.call_mcp_server('get_dhcp_leases')

        elif 'health' in query or 'status' in query:
            result = mcp_client.call_mcp_server('get_system_health')

        elif 'discovery' in query:
            result = mcp_client.call_mcp_server('get_discovery_status')

        elif 'statistics' in query or 'stats' in query:
            if 'dns' in query:
                result = mcp_client.call_mcp_server('get_dns_statistics')
            else:
                result = mcp_client.call_mcp_server('get_dhcp_statistics')
        else:
            # Generic query - try to determine best tool
            result = mcp_client.call_mcp_server('get_system_info')
        
        # Format the response for the web interface
        if isinstance(result, dict) and 'error' in result:
            return jsonify({
                'success': False,
                'response': f"❌ Error: {result['error']}"
            })
        else:
            # Format the result as HTML
            formatted_response = format_infoblox_response(result, query)
            return jsonify({
                'success': True,
                'response': formatted_response
            })
            
    except Exception as e:
        return jsonify({
            'success': False,
            'response': f"❌ Server error: {str(e)}"
        })

def format_infoblox_response(data, query):
    """Format InfoBlox data for display in the web interface - SHOW ACTUAL DATA"""
    if not data:
        return "No data returned from InfoBlox."

    print(f"🔍 DEBUG: Formatting data: {data}")
    print(f"🔍 DEBUG: Data type: {type(data)}")

    # Handle MCP response format that contains zones/networks in nested structure
    if isinstance(data, dict):
        # Check if this is a zones response
        if 'zones' in data and isinstance(data['zones'], list):
            zones = data['zones']
            html = f"<strong>🌐 DNS Zones ({len(zones)} found):</strong><br><br>"
            for zone in zones:
                fqdn = zone.get('fqdn', 'Unknown')
                zone_format = zone.get('zone_format', 'Unknown')
                view = zone.get('view', 'default')
                html += f"• <strong>{fqdn}</strong> ({zone_format}) in view '{view}'<br>"
            return html

        # Check if this is a networks response
        elif 'networks' in data and isinstance(data['networks'], list):
            networks = data['networks']
            html = f"<strong>🔗 DHCP Networks ({len(networks)} found):</strong><br><br>"
            for network in networks:
                network_addr = network.get('network', 'Unknown')
                comment = network.get('comment', 'No description')
                network_view = network.get('network_view', 'default')
                html += f"• <strong>{network_addr}</strong> - {comment} (view: {network_view})<br>"
            return html

        # Check if this is a records response
        elif 'records' in data and isinstance(data['records'], list):
            records = data['records']
            record_type = data.get('record_type', 'DNS')
            html = f"<strong>📝 {record_type} Records ({len(records)} found):</strong><br><br>"
            for record in records:
                name = record.get('name', 'Unknown')
                if 'ipv4addr' in record:
                    html += f"• <strong>{name}</strong> → {record['ipv4addr']}<br>"
                elif 'ipv6addr' in record:
                    html += f"• <strong>{name}</strong> → {record['ipv6addr']}<br>"
                else:
                    html += f"• <strong>{name}</strong><br>"
            return html

        # Generic dict formatting - show all actual data
        else:
            html = "<strong>📊 InfoBlox Response:</strong><br><br>"
            for key, value in data.items():
                if isinstance(value, list):
                    html += f"• <strong>{key}:</strong> {len(value)} items<br>"
                    # Show first few items if it's a list
                    for i, item in enumerate(value[:3]):
                        if isinstance(item, dict):
                            # Show key fields from each item
                            item_desc = []
                            for field in ['fqdn', 'name', 'network', 'host_name']:
                                if field in item:
                                    item_desc.append(f"{field}: {item[field]}")
                            if item_desc:
                                html += f"  - {', '.join(item_desc)}<br>"
                            else:
                                html += f"  - {str(item)[:50]}...<br>"
                        else:
                            html += f"  - {str(item)}<br>"
                    if len(value) > 3:
                        html += f"  - ... and {len(value) - 3} more<br>"
                elif isinstance(value, dict):
                    html += f"• <strong>{key}:</strong> Object with {len(value)} fields<br>"
                else:
                    html += f"• <strong>{key}:</strong> {value}<br>"
            return html

    elif isinstance(data, list):
        # Direct list of items
        html = f"<strong>📋 Results ({len(data)} items):</strong><br><br>"
        for item in data:
            if isinstance(item, dict):
                # Show key fields
                key_field = next((k for k in ['fqdn', 'name', 'network', 'host_name'] if k in item), None)
                if key_field:
                    html += f"• <strong>{item[key_field]}</strong>"
                    # Add additional context
                    if 'zone_format' in item:
                        html += f" ({item['zone_format']})"
                    if 'comment' in item:
                        html += f" - {item['comment']}"
                    html += "<br>"
                else:
                    html += f"• {str(item)[:100]}...<br>"
            else:
                html += f"• {str(item)}<br>"
        return html

    else:
        # Simple string or other data
        return f"<strong>📋 Raw Result:</strong><br><br>{str(data)}"

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({'status': 'healthy', 'service': 'InfoBlox MCP Backend'})

if __name__ == '__main__':
    print("🏨 Starting Marriott InfoBlox MCP Backend Server...")
    print("🔗 Initializing InfoBlox MCP connection...")

    # Initialize the MCP client
    mcp_client = InfoBloxMCPClient()

    print("🚀 Starting Flask server on port 5001...")
    app.run(host='0.0.0.0', port=5001, debug=True)
