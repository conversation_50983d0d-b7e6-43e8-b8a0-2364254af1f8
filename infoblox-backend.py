#!/usr/bin/env python3
"""
InfoBlox MCP Backend Server
Connects the web interface to the actual InfoBlox MCP server
"""

import json
import sys
import os
import asyncio
from flask import Flask, request, jsonify
from flask_cors import CORS

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Import the actual MCP server components
try:
    from infoblox_mcp.tools import ToolRegistry
    from infoblox_mcp.client import InfoBloxClient
    from infoblox_mcp.config import InfoBloxConfig
    print("✅ Successfully imported InfoBlox MCP components")
except ImportError as e:
    print(f"❌ Error importing MCP components: {e}")
    ToolRegistry = None
    InfoBloxClient = None
    InfoBloxConfig = None

app = Flask(__name__)
CORS(app)

class InfoBloxMCPClient:
    def __init__(self):
        """Initialize the real InfoBlox MCP client."""
        self.tool_registry = None
        self.infoblox_client = None
        self._initialize_infoblox_connection()

    def _initialize_infoblox_connection(self):
        """Initialize connection to InfoBlox."""
        try:
            if not InfoBloxConfig or not InfoBloxClient or not ToolRegistry:
                print("❌ InfoBlox MCP components not available")
                return

            # Create configuration from environment variables or defaults
            grid_master_ip = os.environ.get('INFOBLOX_HOST', '************')
            username = os.environ.get('INFOBLOX_USERNAME', 'admin')
            password = os.environ.get('INFOBLOX_PASSWORD', 'infoblox')

            print(f"🔗 Connecting to InfoBlox at {grid_master_ip} as {username}")

            config = InfoBloxConfig(
                grid_master_ip=grid_master_ip,
                username=username,
                password=password,
                wapi_version="v2.12.3",  # Correct WAPI version
                verify_ssl=False  # Disable SSL verification for testing
            )

            # Initialize InfoBlox client with timeout
            print("🔄 Initializing InfoBlox client...")
            self.infoblox_client = InfoBloxClient(config)

            # Test connection with timeout
            print("🔄 Testing InfoBlox connection...")
            if self.infoblox_client.test_connection():
                print("✅ Successfully connected to InfoBlox")

                # Initialize tool registry
                self.tool_registry = ToolRegistry()
                print("✅ InfoBlox tools initialized")
            else:
                print("❌ Failed to connect to InfoBlox - will return error messages")

        except Exception as e:
            print(f"❌ Error initializing InfoBlox connection: {e}")
            print("⚠️  Backend will start without InfoBlox connection")
            self.infoblox_client = None
            self.tool_registry = None

    def call_mcp_server(self, tool_name, query_or_arguments=None):
        """Call the actual InfoBlox MCP tools."""
        try:
            if not self.tool_registry or not self.infoblox_client:
                return {"error": "InfoBlox connection not available. Please check configuration."}

            # Handle both query string and arguments dict
            if isinstance(query_or_arguments, str):
                query = query_or_arguments
                arguments = {}
            else:
                query = query_or_arguments.get('query', '') if isinstance(query_or_arguments, dict) else ''
                arguments = query_or_arguments if isinstance(query_or_arguments, dict) else {}

            # Map simplified tool names to actual MCP tool names
            tool_name_mapping = {
                'get_zones': 'infoblox_dns_list_zones',
                'get_networks': 'infoblox_dhcp_list_networks',
                'get_grid_members': 'infoblox_grid_list_members',
                'get_system_health': 'infoblox_grid_get_status',
                'get_host_records': 'infoblox_dns_search_records',
                'get_a_records': 'infoblox_dns_search_records',
                'get_network_utilization': 'infoblox_ipam_get_network_utilization',
                'get_dhcp_leases': 'infoblox_dhcp_list_networks',
                'get_discovery_status': 'infoblox_grid_get_status',
                'get_dns_statistics': 'infoblox_grid_get_status',
                'get_dhcp_statistics': 'infoblox_grid_get_status',
                'get_system_info': 'infoblox_grid_get_status'
            }

            actual_tool_name = tool_name_mapping.get(tool_name)
            if not actual_tool_name:
                return {"error": f"Unknown tool: {tool_name}"}

            # Prepare arguments for specific tools
            tool_arguments = arguments.copy() if arguments else {}

            # Special handling for specific tools
            if tool_name == 'get_a_records':
                tool_arguments = {"record_type": "A"}
                if arguments and 'zone' in arguments:
                    tool_arguments['zone'] = arguments['zone']
            elif tool_name == 'get_host_records':
                tool_arguments = {"record_type": "A"}
            elif tool_name == 'get_network_utilization':
                # Extract network from query if not provided
                if not tool_arguments.get('network'):
                    # Try to extract network from query
                    import re
                    network_match = re.search(r'(\d+\.\d+\.\d+\.\d+/\d+)', query)
                    if network_match:
                        tool_arguments = {"network": network_match.group(1)}
                    else:
                        # Default to first network we know exists
                        tool_arguments = {"network": "*************/24"}

            # Execute the tool using asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(
                    self.tool_registry.execute_tool(actual_tool_name, tool_arguments, self.infoblox_client)
                )
                # Parse JSON result if it's a string
                if isinstance(result, str):
                    try:
                        return json.loads(result)
                    except json.JSONDecodeError:
                        return {"message": result}
                return result
            finally:
                loop.close()

        except Exception as e:
            return {"error": f"Failed to call InfoBlox tool {tool_name}: {str(e)}"}

mcp_client = InfoBloxMCPClient()

@app.route('/api/query', methods=['POST'])
def handle_query():
    """Handle InfoBlox queries from the web interface"""
    try:
        data = request.get_json()
        query = data.get('query', '').lower()
        use_mcp = data.get('use_mcp', True)  # Default to MCP, but allow direct WAPI

        if use_mcp:
            # Use MCP Server approach
            return handle_mcp_query(query)
        else:
            # Use Direct WAPI approach
            return handle_direct_wapi_query(query)

    except Exception as e:
        return jsonify({
            'success': False,
            'response': f"❌ Server error: {str(e)}"
        })

def handle_mcp_query(query):
    """Handle query through MCP server"""
    # Map natural language queries to MCP tools
    if 'dns zone' in query or 'zones' in query:
        result = mcp_client.call_mcp_server('get_zones', query)

    elif 'dhcp network' in query or 'networks' in query:
        result = mcp_client.call_mcp_server('get_networks', query)

    elif 'grid member' in query or 'members' in query:
        result = mcp_client.call_mcp_server('get_grid_members', query)

    elif 'host record' in query or 'hosts' in query:
        result = mcp_client.call_mcp_server('get_host_records', query)

    elif 'a record' in query and 'example.com' in query:
        result = mcp_client.call_mcp_server('get_a_records', {'zone': 'example.com', 'query': query})

    elif 'utilization' in query or 'usage' in query:
        result = mcp_client.call_mcp_server('get_network_utilization', query)

    elif 'lease' in query or 'dhcp lease' in query:
        result = mcp_client.call_mcp_server('get_dhcp_leases', query)

    elif 'health' in query or 'status' in query:
        result = mcp_client.call_mcp_server('get_system_health', query)

    elif 'discovery' in query:
        result = mcp_client.call_mcp_server('get_discovery_status', query)

    elif 'statistics' in query or 'stats' in query:
        if 'dns' in query:
            result = mcp_client.call_mcp_server('get_dns_statistics', query)
        else:
            result = mcp_client.call_mcp_server('get_dhcp_statistics', query)
    else:
        # Generic query - try to determine best tool
        result = mcp_client.call_mcp_server('get_system_info', query)

    return process_result(result, query, "MCP")

def handle_direct_wapi_query(query):
    """Handle query through direct WAPI calls"""
    try:
        if not mcp_client.infoblox_client:
            return {"error": "Direct WAPI connection not available"}

        client = mcp_client.infoblox_client

        # Map queries to direct WAPI calls
        if 'dns zone' in query or 'zones' in query:
            result = client.search_objects("zone_auth")

        elif 'dhcp network' in query or 'networks' in query:
            result = client.search_objects("network")

        elif 'grid member' in query or 'members' in query:
            result = client.search_objects("member")

        elif 'health' in query or 'status' in query:
            result = client.search_objects("grid")

        elif 'utilization' in query or 'usage' in query:
            # Get network utilization using proper WAPI approach
            result = get_network_utilization_direct(client, query)

        else:
            result = {"message": f"Direct WAPI query for: {query}"}

        return process_result(result, query, "Direct WAPI")

    except Exception as e:
        return {"error": f"Direct WAPI error: {str(e)}"}

def get_network_utilization_direct(client, query):
    """Get network utilization using direct WAPI calls"""
    try:
        # Extract network from query or use default
        import re
        network_match = re.search(r'(\d+\.\d+\.\d+\.\d+/\d+)', query)
        target_network = network_match.group(1) if network_match else "*************/24"

        # First get all networks to find the one we want
        networks = client.search_objects("network")

        utilization_data = []
        for network in networks:
            network_addr = network.get('network', '')
            if target_network == "all" or network_addr == target_network or target_network in network_addr:
                # Get basic network info
                network_info = {
                    'network': network_addr,
                    'comment': network.get('comment', 'No description'),
                    'network_view': network.get('network_view', 'default')
                }

                # Try to get usage statistics (this is a simplified approach)
                # In real InfoBlox, you'd use specific WAPI endpoints for utilization
                try:
                    # Get DHCP ranges for this network to estimate usage
                    ranges = client.search_objects("range", {"network": network_addr})
                    network_info['dhcp_ranges'] = len(ranges)

                    # Get fixed addresses
                    fixed_addrs = client.search_objects("fixedaddress", {"network": network_addr})
                    network_info['fixed_addresses'] = len(fixed_addrs)

                    # Calculate basic utilization estimate
                    import ipaddress
                    net = ipaddress.IPv4Network(network_addr, strict=False)
                    total_ips = net.num_addresses - 2  # Exclude network and broadcast
                    used_estimate = len(fixed_addrs)  # This is a simplified estimate

                    network_info['utilization_estimate'] = {
                        'total_ips': total_ips,
                        'used_ips_estimate': used_estimate,
                        'utilization_percent': (used_estimate / total_ips * 100) if total_ips > 0 else 0
                    }

                except Exception as e:
                    network_info['utilization_error'] = str(e)

                utilization_data.append(network_info)

        return {
            'networks': utilization_data,
            'query_network': target_network,
            'note': 'Direct WAPI utilization is estimated from fixed addresses and ranges'
        }

    except Exception as e:
        return {"error": f"Failed to get utilization: {str(e)}"}

def process_result(result, query, method):
    """Process and format the result"""
    # Format the response for the web interface
    if isinstance(result, dict) and 'error' in result:
        return jsonify({
            'success': False,
            'response': f"❌ Error ({method}): {result['error']}"
        })
    else:
        # Format the result as HTML
        formatted_response = format_infoblox_response(result, query)
        formatted_response += f"<br><br><em>📡 Method: {method}</em>"
        return jsonify({
            'success': True,
            'response': formatted_response
        })

def format_infoblox_response(data, query):
    """Format InfoBlox data for display in the web interface - SHOW ACTUAL DATA"""
    if not data:
        return "No data returned from InfoBlox."

    print(f"🔍 DEBUG: Formatting data: {data}")
    print(f"🔍 DEBUG: Data type: {type(data)}")

    # Handle MCP response format that contains zones/networks in nested structure
    if isinstance(data, dict):
        # Check if this is a zones response
        if 'zones' in data and isinstance(data['zones'], list):
            zones = data['zones']
            html = f"<strong>🌐 DNS Zones ({len(zones)} found):</strong><br><br>"
            for zone in zones:
                fqdn = zone.get('fqdn', 'Unknown')
                zone_format = zone.get('zone_format', 'Unknown')
                view = zone.get('view', 'default')
                html += f"• <strong>{fqdn}</strong> ({zone_format}) in view '{view}'<br>"
            return html

        # Check if this is a networks response
        elif 'networks' in data and isinstance(data['networks'], list):
            networks = data['networks']
            html = f"<strong>🔗 DHCP Networks ({len(networks)} found):</strong><br><br>"
            for network in networks:
                network_addr = network.get('network', 'Unknown')
                comment = network.get('comment', 'No description')
                network_view = network.get('network_view', 'default')
                html += f"• <strong>{network_addr}</strong> - {comment} (view: {network_view})<br>"
            return html

        # Check if this is a records response
        elif 'records' in data and isinstance(data['records'], list):
            records = data['records']
            record_type = data.get('record_type', 'DNS')
            html = f"<strong>📝 {record_type} Records ({len(records)} found):</strong><br><br>"
            for record in records:
                name = record.get('name', 'Unknown')
                if 'ipv4addr' in record:
                    html += f"• <strong>{name}</strong> → {record['ipv4addr']}<br>"
                elif 'ipv6addr' in record:
                    html += f"• <strong>{name}</strong> → {record['ipv6addr']}<br>"
                else:
                    html += f"• <strong>{name}</strong><br>"
            return html

        # Special handling for grid status
        elif 'grid_info' in data or 'status' in data:
            html = "<strong>⚡ Grid System Status:</strong><br><br>"

            if 'status' in data:
                status = data['status']
                status_icon = "✅" if status == "operational" else "⚠️"
                html += f"• <strong>System Status:</strong> {status_icon} {status.title()}<br>"

            if 'grid_info' in data and isinstance(data['grid_info'], list):
                grid_items = data['grid_info']
                html += f"• <strong>Grid Members:</strong> {len(grid_items)} found<br>"
                for item in grid_items:
                    if isinstance(item, dict) and '_ref' in item:
                        # Extract meaningful info from grid reference
                        ref = item['_ref']
                        if 'Infoblox' in ref:
                            html += f"  - InfoBlox Grid Master (Active)<br>"
                        else:
                            html += f"  - Grid Member: {ref.split('/')[-1]}<br>"

            return html

        # Generic dict formatting - show all actual data
        else:
            html = "<strong>📊 InfoBlox Response:</strong><br><br>"
            for key, value in data.items():
                if isinstance(value, list):
                    html += f"• <strong>{key}:</strong> {len(value)} items<br>"
                    # Show first few items if it's a list
                    for _, item in enumerate(value[:3]):
                        if isinstance(item, dict):
                            # Show key fields from each item
                            item_desc = []
                            for field in ['fqdn', 'name', 'network', 'host_name']:
                                if field in item:
                                    item_desc.append(f"{field}: {item[field]}")
                            if item_desc:
                                html += f"  - {', '.join(item_desc)}<br>"
                            else:
                                html += f"  - {str(item)[:50]}...<br>"
                        else:
                            html += f"  - {str(item)}<br>"
                    if len(value) > 3:
                        html += f"  - ... and {len(value) - 3} more<br>"
                elif isinstance(value, dict):
                    html += f"• <strong>{key}:</strong> Object with {len(value)} fields<br>"
                else:
                    html += f"• <strong>{key}:</strong> {value}<br>"
            return html

    elif isinstance(data, list):
        # Direct list of items
        html = f"<strong>📋 Results ({len(data)} items):</strong><br><br>"
        for item in data:
            if isinstance(item, dict):
                # Show key fields
                key_field = next((k for k in ['fqdn', 'name', 'network', 'host_name'] if k in item), None)
                if key_field:
                    html += f"• <strong>{item[key_field]}</strong>"
                    # Add additional context
                    if 'zone_format' in item:
                        html += f" ({item['zone_format']})"
                    if 'comment' in item:
                        html += f" - {item['comment']}"
                    html += "<br>"
                else:
                    html += f"• {str(item)[:100]}...<br>"
            else:
                html += f"• {str(item)}<br>"
        return html

    else:
        # Simple string or other data
        return f"<strong>📋 Raw Result:</strong><br><br>{str(data)}"

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({'status': 'healthy', 'service': 'InfoBlox MCP Backend'})

def find_available_port(start_port=5001):
    """Find an available port starting from start_port"""
    import socket
    for port in range(start_port, start_port + 100):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return port
        except OSError:
            continue
    return None

if __name__ == '__main__':
    print("🏨 Starting Marriott InfoBlox MCP Backend Server...")
    print("🔗 Initializing InfoBlox MCP connection...")

    # Initialize the MCP client
    mcp_client = InfoBloxMCPClient()

    port = find_available_port()
    if port:
        print(f"🚀 Starting Flask server on port {port}...")
        print(f"🌐 Access the API at: http://localhost:{port}")
        app.run(host='0.0.0.0', port=port, debug=True)
    else:
        print("❌ Could not find an available port")
