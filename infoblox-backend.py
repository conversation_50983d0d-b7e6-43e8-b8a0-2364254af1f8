#!/usr/bin/env python3
"""
InfoBlox MCP Backend Server
Connects the web interface to the actual InfoBlox MCP server
"""

import asyncio
import json
import sys
import os
from flask import Flask, request, jsonify
from flask_cors import CORS
import subprocess
import tempfile

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

app = Flask(__name__)
CORS(app)

class InfoBloxMCPClient:
    def __init__(self):
        self.mcp_server_path = os.path.join(os.path.dirname(__file__), 'infoblox-mcp-server.py')
        
    async def call_mcp_server(self, tool_name, arguments=None):
        """Call the InfoBlox MCP server with the specified tool and arguments"""
        try:
            # Create MCP request
            mcp_request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": arguments or {}
                }
            }
            
            # Write request to temporary file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                json.dump(mcp_request, f)
                request_file = f.name
            
            try:
                # Call the MCP server
                result = subprocess.run([
                    'python3', self.mcp_server_path
                ], 
                input=json.dumps(mcp_request),
                capture_output=True,
                text=True,
                timeout=30
                )
                
                if result.returncode == 0:
                    response = json.loads(result.stdout)
                    return response.get('result', {})
                else:
                    return {"error": f"MCP server error: {result.stderr}"}
                    
            finally:
                # Clean up temp file
                if os.path.exists(request_file):
                    os.unlink(request_file)
                    
        except Exception as e:
            return {"error": f"Failed to call MCP server: {str(e)}"}

mcp_client = InfoBloxMCPClient()

@app.route('/api/query', methods=['POST'])
async def handle_query():
    """Handle InfoBlox queries from the web interface"""
    try:
        data = request.get_json()
        query = data.get('query', '').lower()
        
        # Map natural language queries to MCP tools
        if 'dns zone' in query or 'zones' in query:
            result = await mcp_client.call_mcp_server('get_zones')
            
        elif 'dhcp network' in query or 'networks' in query:
            result = await mcp_client.call_mcp_server('get_networks')
            
        elif 'grid member' in query or 'members' in query:
            result = await mcp_client.call_mcp_server('get_grid_members')
            
        elif 'host record' in query or 'hosts' in query:
            result = await mcp_client.call_mcp_server('get_host_records')
            
        elif 'a record' in query and 'example.com' in query:
            result = await mcp_client.call_mcp_server('get_a_records', {'zone': 'example.com'})
            
        elif 'utilization' in query or 'usage' in query:
            result = await mcp_client.call_mcp_server('get_network_utilization')
            
        elif 'lease' in query or 'dhcp lease' in query:
            result = await mcp_client.call_mcp_server('get_dhcp_leases')
            
        elif 'health' in query or 'status' in query:
            result = await mcp_client.call_mcp_server('get_system_health')
            
        elif 'discovery' in query:
            result = await mcp_client.call_mcp_server('get_discovery_status')
            
        elif 'statistics' in query or 'stats' in query:
            if 'dns' in query:
                result = await mcp_client.call_mcp_server('get_dns_statistics')
            else:
                result = await mcp_client.call_mcp_server('get_dhcp_statistics')
        else:
            # Generic query - try to determine best tool
            result = await mcp_client.call_mcp_server('get_system_info')
        
        # Format the response for the web interface
        if 'error' in result:
            return jsonify({
                'success': False,
                'response': f"❌ Error: {result['error']}"
            })
        else:
            # Format the result as HTML
            formatted_response = format_infoblox_response(result, query)
            return jsonify({
                'success': True,
                'response': formatted_response
            })
            
    except Exception as e:
        return jsonify({
            'success': False,
            'response': f"❌ Server error: {str(e)}"
        })

def format_infoblox_response(data, query):
    """Format InfoBlox data for display in the web interface"""
    if not data:
        return "No data returned from InfoBlox."
    
    # Handle different types of responses
    if isinstance(data, list):
        if not data:
            return "No results found."
        
        # Format list data
        if 'zone' in query.lower():
            html = "<strong>🌐 DNS Zones:</strong><br><br>"
            for item in data:
                zone_name = item.get('fqdn', item.get('zone', 'Unknown'))
                zone_type = item.get('zone_format', 'Unknown')
                html += f"• <strong>{zone_name}</strong> - {zone_type}<br>"
            return html
            
        elif 'network' in query.lower():
            html = "<strong>🔗 DHCP Networks:</strong><br><br>"
            for item in data:
                network = item.get('network', 'Unknown')
                comment = item.get('comment', 'No description')
                html += f"• <strong>{network}</strong> - {comment}<br>"
            return html
            
        elif 'member' in query.lower():
            html = "<strong>⚡ Grid Members:</strong><br><br>"
            for item in data:
                hostname = item.get('host_name', 'Unknown')
                status = item.get('node_info', [{}])[0].get('status', 'Unknown')
                html += f"• <strong>{hostname}</strong> - {status}<br>"
            return html
            
        else:
            # Generic list formatting
            html = "<strong>📋 Results:</strong><br><br>"
            for i, item in enumerate(data[:10]):  # Limit to 10 items
                if isinstance(item, dict):
                    key_field = next((k for k in ['name', 'fqdn', 'host_name', 'network'] if k in item), None)
                    if key_field:
                        html += f"• <strong>{item[key_field]}</strong><br>"
                    else:
                        html += f"• Item {i+1}: {str(item)[:100]}...<br>"
                else:
                    html += f"• {str(item)}<br>"
            
            if len(data) > 10:
                html += f"<br><em>... and {len(data) - 10} more items</em>"
            return html
    
    elif isinstance(data, dict):
        # Format dictionary data
        html = "<strong>📊 InfoBlox Data:</strong><br><br>"
        for key, value in data.items():
            if isinstance(value, (list, dict)):
                html += f"• <strong>{key}:</strong> {len(value) if isinstance(value, list) else 'Object'}<br>"
            else:
                html += f"• <strong>{key}:</strong> {value}<br>"
        return html
    
    else:
        # Simple string or other data
        return f"<strong>📋 Result:</strong><br><br>{str(data)}"

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({'status': 'healthy', 'service': 'InfoBlox MCP Backend'})

if __name__ == '__main__':
    print("🏨 Starting Marriott InfoBlox MCP Backend Server...")
    print("🔗 Connecting to InfoBlox MCP server...")
    app.run(host='0.0.0.0', port=5001, debug=True)
