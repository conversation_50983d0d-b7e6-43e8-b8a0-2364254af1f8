/* Marriott-Inspired Corporate Theme */
:root {
  /* Marriott Brand Colors - Sophisticated Burgundy & Gold Palette */
  --corporate-primary: #8B1538;        /* Marriott Deep Burgundy */
  --corporate-secondary: #B8860B;      /* Marriott Gold */
  --corporate-accent: #2C5F41;         /* Marriott Forest Green */
  --corporate-tertiary: #4A5568;       /* Sophisticated Charcoal */

  /* Marriott Neutral Palette - Warm & Luxurious */
  --corporate-gray-50: #FEFEFE;        /* Pure White */
  --corporate-gray-100: #F7F7F7;       /* Warm Off-White */
  --corporate-gray-200: #EEEEEE;       /* Light Warm Gray */
  --corporate-gray-300: #D4D4D4;       /* Medium Light Gray */
  --corporate-gray-400: #A3A3A3;       /* Medium Gray */
  --corporate-gray-500: #737373;       /* Neutral Gray */
  --corporate-gray-600: #525252;       /* Dark Gray */
  --corporate-gray-700: #404040;       /* Charcoal */
  --corporate-gray-800: #262626;       /* Deep Charcoal */
  --corporate-gray-900: #171717;       /* Near Black */

  /* Marriott Status Colors - Elegant & Professional */
  --status-success: #059669;           /* Emerald Green */
  --status-warning: #D97706;           /* Warm Amber */
  --status-error: #DC2626;             /* Sophisticated Red */
  --status-info: #2563EB;              /* Professional Blue */

  /* InfoBlox Service Colors - Marriott-Inspired */
  --infoblox-network: #7C3AED;         /* Royal Purple */
  --infoblox-dns: #0891B2;             /* Ocean Blue */
  --infoblox-dhcp: #65A30D;            /* Forest Green */
  --infoblox-grid: #EA580C;            /* Warm Orange */
  --infoblox-ipam: #BE185D;            /* Rose */

  /* Marriott Luxury Gradients */
  --gradient-primary: linear-gradient(135deg, #8B1538 0%, #A91D47 100%);
  --gradient-secondary: linear-gradient(135deg, #B8860B 0%, #DAA520 100%);
  --gradient-hero: linear-gradient(135deg, #8B1538 0%, #2C5F41 100%);
  --gradient-card: linear-gradient(145deg, #FFFFFF 0%, #F7F7F7 100%);

  /* Marriott Shadows - Sophisticated Depth */
  --shadow-sm: 0 1px 2px 0 rgba(139, 21, 56, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(139, 21, 56, 0.1), 0 2px 4px -1px rgba(139, 21, 56, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(139, 21, 56, 0.1), 0 4px 6px -2px rgba(139, 21, 56, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(139, 21, 56, 0.1), 0 10px 10px -5px rgba(139, 21, 56, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(139, 21, 56, 0.25);
}

/* Marriott Typography - Elegant & Professional */
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Source+Sans+Pro:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap');

.marriott-font-display {
  font-family: 'Playfair Display', Georgia, serif;
  font-weight: 500;
  letter-spacing: -0.025em;
}

.marriott-font-primary {
  font-family: 'Source Sans Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-weight: 400;
  letter-spacing: -0.01em;
}

.marriott-font-mono {
  font-family: 'JetBrains Mono', 'SF Mono', Monaco, 'Cascadia Code', monospace;
  font-weight: 400;
}

/* Legacy support */
.corporate-font-primary { @extend .marriott-font-primary; }
.corporate-font-mono { @extend .marriott-font-mono; }
.corporate-font-display { @extend .marriott-font-display; }

/* Marriott Component Styles - Luxury & Sophistication */

/* Marriott Button System */
.marriott-button {
  @apply px-6 py-3 rounded-sm font-medium transition-all duration-300 ease-in-out;
  font-family: 'Source Sans Pro', sans-serif;
  font-weight: 500;
  letter-spacing: 0.025em;
  text-transform: uppercase;
  font-size: 0.875rem;
  background: var(--gradient-primary);
  color: white;
  border: none;
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
}

.marriott-button:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
  background: linear-gradient(135deg, #A91D47 0%, #8B1538 100%);
}

.marriott-button:active {
  transform: translateY(0);
  box-shadow: var(--shadow-md);
}

.marriott-button-secondary {
  @apply px-6 py-3 rounded-sm font-medium transition-all duration-300;
  font-family: 'Source Sans Pro', sans-serif;
  font-weight: 500;
  letter-spacing: 0.025em;
  text-transform: uppercase;
  font-size: 0.875rem;
  background: transparent;
  color: var(--corporate-primary);
  border: 2px solid var(--corporate-primary);
  box-shadow: none;
}

.marriott-button-secondary:hover {
  background: var(--corporate-primary);
  color: white;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.marriott-button-gold {
  @apply px-6 py-3 rounded-sm font-medium transition-all duration-300;
  background: var(--gradient-secondary);
  color: var(--corporate-gray-900);
  font-weight: 600;
}

.marriott-button-gold:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
  background: linear-gradient(135deg, #DAA520 0%, #B8860B 100%);
}

/* Marriott Card System */
.marriott-card {
  @apply bg-white rounded-lg border border-gray-200 overflow-hidden;
  background: var(--gradient-card);
  border-color: var(--corporate-gray-200);
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease-in-out;
}

.marriott-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
  border-color: var(--corporate-gray-300);
}

.marriott-card-luxury {
  @apply bg-white rounded-lg border overflow-hidden;
  background: linear-gradient(145deg, #FFFFFF 0%, #FEFEFE 100%);
  border: 1px solid var(--corporate-gray-200);
  box-shadow: var(--shadow-lg);
  position: relative;
}

.marriott-card-luxury::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
}

/* Marriott Header System */
.marriott-header {
  background: var(--gradient-hero);
  color: white;
  padding: 1.5rem 0;
  box-shadow: var(--shadow-lg);
  position: relative;
}

.marriott-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--gradient-secondary);
}

.marriott-nav {
  @apply bg-white border-b;
  border-color: var(--corporate-gray-200);
  box-shadow: var(--shadow-sm);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
}

/* Legacy support */
.corporate-button { @extend .marriott-button; }
.corporate-card { @extend .marriott-card; }
.corporate-header { @extend .marriott-header; }

/* Marriott-Inspired InfoBlox Component Styles */

/* InfoBlox Service Cards - Marriott Luxury Style */
.infoblox-network-card {
  @extend .marriott-card-luxury;
  border-left: 6px solid var(--infoblox-network);
  position: relative;
}

.infoblox-network-card::before {
  background: linear-gradient(135deg, var(--infoblox-network), rgba(124, 58, 237, 0.1));
}

.infoblox-dns-card {
  @extend .marriott-card-luxury;
  border-left: 6px solid var(--infoblox-dns);
}

.infoblox-dns-card::before {
  background: linear-gradient(135deg, var(--infoblox-dns), rgba(8, 145, 178, 0.1));
}

.infoblox-dhcp-card {
  @extend .marriott-card-luxury;
  border-left: 6px solid var(--infoblox-dhcp);
}

.infoblox-dhcp-card::before {
  background: linear-gradient(135deg, var(--infoblox-dhcp), rgba(101, 163, 13, 0.1));
}

.infoblox-grid-card {
  @extend .marriott-card-luxury;
  border-left: 6px solid var(--infoblox-grid);
}

.infoblox-grid-card::before {
  background: linear-gradient(135deg, var(--infoblox-grid), rgba(234, 88, 12, 0.1));
}

.infoblox-ipam-card {
  @extend .marriott-card-luxury;
  border-left: 6px solid var(--infoblox-ipam);
}

.infoblox-ipam-card::before {
  background: linear-gradient(135deg, var(--infoblox-ipam), rgba(190, 24, 93, 0.1));
}

/* Marriott-Style InfoBlox Badges */
.infoblox-badge {
  @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium;
  font-family: 'Source Sans Pro', sans-serif;
  font-weight: 600;
  letter-spacing: 0.025em;
  text-transform: uppercase;
}

.infoblox-badge-network {
  background: rgba(124, 58, 237, 0.1);
  color: var(--infoblox-network);
  border: 1px solid rgba(124, 58, 237, 0.2);
}

.infoblox-badge-dns {
  background: rgba(8, 145, 178, 0.1);
  color: var(--infoblox-dns);
  border: 1px solid rgba(8, 145, 178, 0.2);
}

.infoblox-badge-dhcp {
  background: rgba(101, 163, 13, 0.1);
  color: var(--infoblox-dhcp);
  border: 1px solid rgba(101, 163, 13, 0.2);
}

.infoblox-badge-grid {
  background: rgba(234, 88, 12, 0.1);
  color: var(--infoblox-grid);
  border: 1px solid rgba(234, 88, 12, 0.2);
}

.infoblox-badge-ipam {
  background: rgba(190, 24, 93, 0.1);
  color: var(--infoblox-ipam);
  border: 1px solid rgba(190, 24, 93, 0.2);
}

/* Marriott-Style Status Indicators */
.marriott-status-indicator {
  @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-medium;
  font-family: 'Source Sans Pro', sans-serif;
  font-weight: 500;
}

.marriott-status-online {
  background: rgba(5, 150, 105, 0.1);
  color: var(--status-success);
  border: 1px solid rgba(5, 150, 105, 0.2);
}

.marriott-status-offline {
  background: rgba(220, 38, 38, 0.1);
  color: var(--status-error);
  border: 1px solid rgba(220, 38, 38, 0.2);
}

.marriott-status-warning {
  background: rgba(217, 119, 6, 0.1);
  color: var(--status-warning);
  border: 1px solid rgba(217, 119, 6, 0.2);
}

.marriott-status-info {
  background: rgba(37, 99, 235, 0.1);
  color: var(--status-info);
  border: 1px solid rgba(37, 99, 235, 0.2);
}

/* Marriott-Style Tables - Elegant Data Display */
.marriott-table {
  @apply w-full border-collapse;
  font-family: 'Source Sans Pro', sans-serif;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--corporate-gray-200);
}

.marriott-table thead {
  background: var(--gradient-primary);
  color: white;
}

.marriott-table thead th {
  @apply px-6 py-4 text-left text-xs font-semibold uppercase tracking-wider;
  font-family: 'Source Sans Pro', sans-serif;
  font-weight: 600;
  letter-spacing: 0.05em;
}

.marriott-table tbody tr {
  @apply border-b border-gray-200;
  transition: all 0.2s ease-in-out;
}

.marriott-table tbody tr:hover {
  background: rgba(139, 21, 56, 0.02);
  transform: scale(1.001);
}

.marriott-table tbody tr:last-child {
  border-bottom: none;
}

.marriott-table tbody td {
  @apply px-6 py-4 text-sm;
  color: var(--corporate-gray-700);
  font-weight: 400;
}

.marriott-table tbody td:first-child {
  font-weight: 500;
  color: var(--corporate-gray-900);
}

/* Marriott-Style Forms - Sophisticated Input Design */
.marriott-form-group {
  @apply mb-6;
}

.marriott-label {
  @apply block text-sm font-medium mb-2;
  font-family: 'Source Sans Pro', sans-serif;
  color: var(--corporate-gray-700);
  font-weight: 500;
  letter-spacing: 0.025em;
}

.marriott-input {
  @apply w-full px-4 py-3 border rounded-lg transition-all duration-200;
  font-family: 'Source Sans Pro', sans-serif;
  border-color: var(--corporate-gray-300);
  background: white;
  color: var(--corporate-gray-900);
  font-size: 0.95rem;
  box-shadow: var(--shadow-sm);
}

.marriott-input:focus {
  outline: none;
  border-color: var(--corporate-primary);
  box-shadow: 0 0 0 3px rgba(139, 21, 56, 0.1), var(--shadow-md);
  transform: translateY(-1px);
}

.marriott-input::placeholder {
  color: var(--corporate-gray-400);
  font-style: italic;
}

.marriott-select {
  @extend .marriott-input;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

/* Marriott-Style Progress Bars - Luxury Indicators */
.marriott-progress {
  @apply w-full bg-gray-200 rounded-full h-3 overflow-hidden;
  background: var(--corporate-gray-200);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.marriott-progress-bar {
  @apply h-full rounded-full transition-all duration-500 ease-out;
  background: var(--gradient-primary);
  box-shadow: 0 2px 4px rgba(139, 21, 56, 0.3);
  position: relative;
}

.marriott-progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.marriott-progress-network {
  background: linear-gradient(135deg, var(--infoblox-network), rgba(124, 58, 237, 0.8));
}

.marriott-progress-dns {
  background: linear-gradient(135deg, var(--infoblox-dns), rgba(8, 145, 178, 0.8));
}

.marriott-progress-dhcp {
  background: linear-gradient(135deg, var(--infoblox-dhcp), rgba(101, 163, 13, 0.8));
}

/* Marriott-Style Alerts - Elegant Notifications */
.marriott-alert {
  @apply p-4 rounded-lg border-l-4;
  font-family: 'Source Sans Pro', sans-serif;
  background: white;
  box-shadow: var(--shadow-md);
  margin-bottom: 1rem;
}

.marriott-alert-success {
  border-left-color: var(--status-success);
  background: linear-gradient(135deg, rgba(5, 150, 105, 0.05), rgba(5, 150, 105, 0.02));
  color: var(--status-success);
}

.marriott-alert-warning {
  border-left-color: var(--status-warning);
  background: linear-gradient(135deg, rgba(217, 119, 6, 0.05), rgba(217, 119, 6, 0.02));
  color: var(--status-warning);
}

.marriott-alert-error {
  border-left-color: var(--status-error);
  background: linear-gradient(135deg, rgba(220, 38, 38, 0.05), rgba(220, 38, 38, 0.02));
  color: var(--status-error);
}

.marriott-alert-info {
  border-left-color: var(--status-info);
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.05), rgba(37, 99, 235, 0.02));
  color: var(--status-info);
}

/* Marriott-Style Chat Interface - Luxury Conversation Design */
.marriott-chat-container {
  @apply flex flex-col h-full;
  background: linear-gradient(145deg, #FEFEFE 0%, #F7F7F7 100%);
  border-radius: 12px;
  box-shadow: var(--shadow-lg);
  overflow: hidden;
}

.marriott-chat-header {
  @apply px-6 py-4 border-b;
  background: var(--gradient-primary);
  color: white;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.marriott-chat-header h3 {
  @apply text-lg font-semibold;
  font-family: 'Playfair Display', serif;
  font-weight: 600;
}

.marriott-chat-messages {
  @apply flex-1 p-4 overflow-y-auto;
  background: white;
}

.marriott-message {
  @apply mb-4 max-w-3xl;
  font-family: 'Source Sans Pro', sans-serif;
}

.marriott-message-user {
  @apply ml-auto;
}

.marriott-message-user .marriott-message-content {
  @apply bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg px-4 py-3;
  background: var(--gradient-primary);
  border-radius: 18px 18px 4px 18px;
  box-shadow: var(--shadow-md);
}

.marriott-message-assistant .marriott-message-content {
  @apply bg-white border rounded-lg px-4 py-3;
  border-color: var(--corporate-gray-200);
  border-radius: 18px 18px 18px 4px;
  box-shadow: var(--shadow-sm);
  background: linear-gradient(145deg, #FFFFFF 0%, #FEFEFE 100%);
}

.marriott-chat-input {
  @apply p-4 border-t;
  background: white;
  border-top: 1px solid var(--corporate-gray-200);
}

.marriott-chat-input-field {
  @extend .marriott-input;
  @apply rounded-full pr-12;
  border-radius: 25px;
  padding-right: 3rem;
}

.marriott-chat-send-button {
  @apply absolute right-2 top-1/2 transform -translate-y-1/2 p-2 rounded-full;
  background: var(--gradient-primary);
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease-in-out;
}

.marriott-chat-send-button:hover {
  transform: translateY(-50%) scale(1.05);
  box-shadow: var(--shadow-md);
}

/* Marriott-Style Dashboard Widgets */
.marriott-widget {
  @extend .marriott-card-luxury;
  @apply p-6;
}

.marriott-widget-header {
  @apply flex items-center justify-between mb-4;
}

.marriott-widget-title {
  @apply text-lg font-semibold;
  font-family: 'Playfair Display', serif;
  color: var(--corporate-gray-900);
  font-weight: 600;
}

.marriott-widget-icon {
  @apply w-8 h-8 rounded-full flex items-center justify-center;
  background: var(--gradient-primary);
  color: white;
}

.marriott-metric {
  @apply text-center p-4;
}

.marriott-metric-value {
  @apply text-3xl font-bold mb-1;
  font-family: 'Playfair Display', serif;
  color: var(--corporate-primary);
  font-weight: 700;
}

.marriott-metric-label {
  @apply text-sm text-gray-600 uppercase tracking-wide;
  font-family: 'Source Sans Pro', sans-serif;
  font-weight: 500;
  letter-spacing: 0.05em;
}

.marriott-metric-change {
  @apply text-xs mt-1;
  font-family: 'Source Sans Pro', sans-serif;
  font-weight: 500;
}

.marriott-metric-change.positive {
  color: var(--status-success);
}

.marriott-metric-change.negative {
  color: var(--status-error);
}

/* Marriott-Style Navigation */
.marriott-sidebar {
  @apply w-64 bg-white border-r h-full;
  border-color: var(--corporate-gray-200);
  background: linear-gradient(180deg, #FFFFFF 0%, #F7F7F7 100%);
  box-shadow: var(--shadow-lg);
}

.marriott-sidebar-header {
  @apply p-6 border-b;
  border-color: var(--corporate-gray-200);
  background: var(--gradient-primary);
  color: white;
}

.marriott-sidebar-logo {
  @apply text-xl font-bold;
  font-family: 'Playfair Display', serif;
  font-weight: 700;
}

.marriott-nav-item {
  @apply flex items-center px-6 py-3 text-gray-700 hover:bg-gray-50 transition-colors duration-200;
  font-family: 'Source Sans Pro', sans-serif;
  font-weight: 500;
  border-left: 4px solid transparent;
}

.marriott-nav-item:hover {
  background: rgba(139, 21, 56, 0.05);
  border-left-color: var(--corporate-primary);
  color: var(--corporate-primary);
}

.marriott-nav-item.active {
  background: rgba(139, 21, 56, 0.1);
  border-left-color: var(--corporate-primary);
  color: var(--corporate-primary);
  font-weight: 600;
}

.marriott-nav-icon {
  @apply w-5 h-5 mr-3;
}

/* Marriott-Style Responsive Design */
@media (max-width: 768px) {
  .marriott-sidebar {
    @apply w-full h-auto;
  }

  .marriott-chat-container {
    @apply h-screen;
  }

  .marriott-widget {
    @apply p-4;
  }

  .marriott-table {
    @apply text-sm;
  }

  .marriott-table thead th,
  .marriott-table tbody td {
    @apply px-3 py-2;
  }
}

/* Marriott-Style Loading States */
.marriott-loading {
  @apply inline-block w-6 h-6 border-2 border-gray-300 border-t-primary rounded-full animate-spin;
  border-top-color: var(--corporate-primary);
}

.marriott-skeleton {
  @apply bg-gray-200 rounded animate-pulse;
  background: linear-gradient(90deg, var(--corporate-gray-200) 25%, var(--corporate-gray-100) 50%, var(--corporate-gray-200) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Marriott-Style Animations */
.marriott-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.marriott-slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
