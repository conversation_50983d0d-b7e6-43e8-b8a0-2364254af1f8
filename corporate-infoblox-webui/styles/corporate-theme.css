/* Corporate Theme Configuration */
:root {
  /* Corporate Brand Colors */
  --corporate-primary: #1e40af;
  --corporate-secondary: #059669;
  --corporate-accent: #dc2626;
  
  /* Corporate Neutral Palette */
  --corporate-gray-50: #f9fafb;
  --corporate-gray-100: #f3f4f6;
  --corporate-gray-200: #e5e7eb;
  --corporate-gray-300: #d1d5db;
  --corporate-gray-400: #9ca3af;
  --corporate-gray-500: #6b7280;
  --corporate-gray-600: #4b5563;
  --corporate-gray-700: #374151;
  --corporate-gray-800: #1f2937;
  --corporate-gray-900: #111827;
  
  /* Status Colors */
  --status-success: #10b981;
  --status-warning: #f59e0b;
  --status-error: #ef4444;
  --status-info: #3b82f6;
  
  /* InfoBlox Specific Colors */
  --infoblox-network: #8b5cf6;
  --infoblox-dns: #06b6d4;
  --infoblox-dhcp: #84cc16;
  --infoblox-grid: #f97316;
  --infoblox-ipam: #ec4899;
}

/* Corporate Typography */
.corporate-font-primary {
  font-family: 'Inter', 'Segoe UI', system-ui, sans-serif;
}

.corporate-font-mono {
  font-family: 'JetBrains Mono', 'Fira Code', monospace;
}

.corporate-font-display {
  font-family: 'Poppins', 'Inter', sans-serif;
}

/* Corporate Component Styles */
.corporate-button {
  @apply px-4 py-2 rounded-lg font-medium transition-colors duration-200;
  background-color: var(--corporate-primary);
  color: white;
}

.corporate-button:hover {
  filter: brightness(110%);
}

.corporate-card {
  @apply bg-white rounded-lg shadow-sm border;
  border-color: var(--corporate-gray-200);
}

.corporate-header {
  background: linear-gradient(135deg, var(--corporate-primary), var(--corporate-secondary));
  color: white;
}

/* InfoBlox Component Styles */
.infoblox-network-card {
  border-left: 4px solid var(--infoblox-network);
}

.infoblox-dns-card {
  border-left: 4px solid var(--infoblox-dns);
}

.infoblox-dhcp-card {
  border-left: 4px solid var(--infoblox-dhcp);
}

.infoblox-grid-card {
  border-left: 4px solid var(--infoblox-grid);
}

.infoblox-ipam-card {
  border-left: 4px solid var(--infoblox-ipam);
}
