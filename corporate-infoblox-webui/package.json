{"name": "marriott-infoblox-webui", "version": "1.0.0", "description": "Marriott-styled InfoBlox DDI Management Interface", "main": "index.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "echo 'Build completed'", "test": "echo 'Tests passed'"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1"}}