"""
Corporate Settings Configuration
"""

import os
from typing import Optional

class CorporateSettings:
    """Corporate-specific settings and configuration."""
    
    # Corporate Branding
    CORPORATE_NAME: str = "Marriot DDI AI"
    CORPORATE_DOMAIN: str = "m"
    CORPORATE_LOGO_URL: str = "/static/corporate/logos/logo.png"
    CORPORATE_FAVICON_URL: str = "/static/corporate/icons/favicon.ico"
    
    # Theme Configuration
    PRIMARY_COLOR: str = "#1e40af"
    SECONDARY_COLOR: str = "#059669"
    ACCENT_COLOR: str = "#dc2626"
    
    # Authentication
    ENABLE_CORPORATE_SSO: bool = os.getenv("ENABLE_CORPORATE_SSO", "false").lower() == "true"
    LDAP_SERVER: Optional[str] = os.getenv("LDAP_SERVER")
    LDAP_BASE_DN: Optional[str] = os.getenv("LDAP_BASE_DN")
    SAML_METADATA_URL: Optional[str] = os.getenv("SAML_METADATA_URL")
    
    # InfoBlox Integration
    ENABLE_INFOBLOX: bool = os.getenv("ENABLE_INFOBLOX", "true").lower() == "true"
    INFOBLOX_MCP_SERVER_PATH: str = os.getenv("INFOBLOX_MCP_SERVER_PATH", "python3 infoblox-mcp-server.py")
    
    # Features
    ENABLE_AUDIT_LOGGING: bool = os.getenv("ENABLE_AUDIT_LOGGING", "true").lower() == "true"
    ENABLE_RBAC: bool = os.getenv("ENABLE_RBAC", "true").lower() == "true"
    
    # Database
    DATABASE_URL: str = os.getenv("DATABASE_URL", "sqlite:///./corporate_webui.db")
    
    @classmethod
    def get_theme_config(cls) -> dict:
        """Get theme configuration for frontend."""
        return {
            "corporateName": cls.CORPORATE_NAME,
            "corporateDomain": cls.CORPORATE_DOMAIN,
            "logoUrl": cls.CORPORATE_LOGO_URL,
            "faviconUrl": cls.CORPORATE_FAVICON_URL,
            "primaryColor": cls.PRIMARY_COLOR,
            "secondaryColor": cls.SECONDARY_COLOR,
            "accentColor": cls.ACCENT_COLOR,
        }
