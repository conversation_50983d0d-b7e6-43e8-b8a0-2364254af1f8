"""
Corporate Settings Configuration
"""

import os
from typing import Optional

class CorporateSettings:
    """Corporate-specific settings and configuration."""
    
    # Corporate Branding - Marriott-Inspired
    CORPORATE_NAME: str = "Marriott InfoBlox DDI Management"
    CORPORATE_DOMAIN: str = "marriott.com"
    CORPORATE_LOGO_URL: str = "/static/corporate/logos/marriott-logo.png"
    CORPORATE_FAVICON_URL: str = "/static/corporate/icons/marriott-favicon.ico"

    # Marriott-Inspired Theme Configuration
    PRIMARY_COLOR: str = "#8B1538"        # Marriott Deep Burgundy
    SECONDARY_COLOR: str = "#B8860B"      # Marriott Gold
    ACCENT_COLOR: str = "#2C5F41"         # Marriott Forest Green
    TERTIARY_COLOR: str = "#4A5568"       # Sophisticated Charcoal

    # Marriott Design System
    DESIGN_SYSTEM: str = "marriott"
    FONT_DISPLAY: str = "Playfair Display"
    FONT_PRIMARY: str = "Source Sans Pro"
    FONT_MONO: str = "JetBrains Mono"
    
    # Authentication
    ENABLE_CORPORATE_SSO: bool = os.getenv("ENABLE_CORPORATE_SSO", "false").lower() == "true"
    LDAP_SERVER: Optional[str] = os.getenv("LDAP_SERVER")
    LDAP_BASE_DN: Optional[str] = os.getenv("LDAP_BASE_DN")
    SAML_METADATA_URL: Optional[str] = os.getenv("SAML_METADATA_URL")
    
    # InfoBlox Integration
    ENABLE_INFOBLOX: bool = os.getenv("ENABLE_INFOBLOX", "true").lower() == "true"
    INFOBLOX_MCP_SERVER_PATH: str = os.getenv("INFOBLOX_MCP_SERVER_PATH", "python3 infoblox-mcp-server.py")
    
    # Features
    ENABLE_AUDIT_LOGGING: bool = os.getenv("ENABLE_AUDIT_LOGGING", "true").lower() == "true"
    ENABLE_RBAC: bool = os.getenv("ENABLE_RBAC", "true").lower() == "true"
    
    # Database
    DATABASE_URL: str = os.getenv("DATABASE_URL", "sqlite:///./corporate_webui.db")
    
    @classmethod
    def get_theme_config(cls) -> dict:
        """Get theme configuration for frontend."""
        return {
            "corporateName": cls.CORPORATE_NAME,
            "corporateDomain": cls.CORPORATE_DOMAIN,
            "logoUrl": cls.CORPORATE_LOGO_URL,
            "faviconUrl": cls.CORPORATE_FAVICON_URL,
            "primaryColor": cls.PRIMARY_COLOR,
            "secondaryColor": cls.SECONDARY_COLOR,
            "accentColor": cls.ACCENT_COLOR,
        }
