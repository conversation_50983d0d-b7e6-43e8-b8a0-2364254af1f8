"""
InfoBlox Integration Configuration
"""

import os
from typing import List, Optional

class InfoBloxConfig:
    """InfoBlox-specific configuration."""
    
    # MCP Server Configuration
    MCP_SERVER_PATH: str = os.getenv("INFOBLOX_MCP_SERVER_PATH", "python3 infoblox-mcp-server.py")
    MCP_SERVER_TIMEOUT: int = int(os.getenv("INFOBLOX_MCP_TIMEOUT", "30"))
    
    # LLM Configuration
    OPENAI_API_KEY: Optional[str] = os.getenv("OPENAI_API_KEY")
    ANTHROPIC_API_KEY: Optional[str] = os.getenv("ANTHROPIC_API_KEY")
    GROQ_API_KEY: Optional[str] = os.getenv("GROQ_API_KEY")
    TOGETHER_API_KEY: Optional[str] = os.getenv("TOGETHER_API_KEY")
    
    # Intent Recognition
    INTENT_CONFIDENCE_THRESHOLD: float = float(os.getenv("INTENT_CONFIDENCE_THRESHOLD", "0.6"))
    LLM_CONFIDENCE_THRESHOLD: float = float(os.getenv("LLM_CONFIDENCE_THRESHOLD", "0.5"))
    
    # Response Formatting
    MAX_TABLE_ROWS: int = int(os.getenv("MAX_TABLE_ROWS", "20"))
    MAX_CELL_LENGTH: int = int(os.getenv("MAX_CELL_LENGTH", "50"))
    
    @classmethod
    def get_available_llm_providers(cls) -> List[str]:
        """Get list of available LLM providers."""
        providers = []
        if cls.OPENAI_API_KEY:
            providers.append("openai")
        if cls.ANTHROPIC_API_KEY:
            providers.append("anthropic")
        if cls.GROQ_API_KEY:
            providers.append("groq")
        if cls.TOGETHER_API_KEY:
            providers.append("together")
        return providers
