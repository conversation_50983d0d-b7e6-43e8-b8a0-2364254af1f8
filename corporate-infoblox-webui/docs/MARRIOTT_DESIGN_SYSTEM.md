# Marriott-Inspired Design System for InfoBlox WebUI

## 🎨 Design Philosophy

This design system draws inspiration from <PERSON><PERSON><PERSON>'s sophisticated, luxury hospitality brand to create an elegant and professional interface for InfoBlox DDI management. The design emphasizes:

- **Luxury & Sophistication**: Premium feel with refined aesthetics
- **Professional Trust**: Corporate-grade reliability and security
- **Elegant Simplicity**: Clean, uncluttered interfaces
- **Warm Hospitality**: Welcoming and user-friendly experience

## 🎯 Brand Colors

### Primary Palette
```css
--corporate-primary: #8B1538;     /* Marriott Deep Burgundy */
--corporate-secondary: #B8860B;   /* Marriott Gold */
--corporate-accent: #2C5F41;      /* Marriott Forest Green */
--corporate-tertiary: #4A5568;    /* Sophisticated Charcoal */
```

### Neutral Palette
```css
--corporate-gray-50: #FEFEFE;     /* Pure White */
--corporate-gray-100: #F7F7F7;    /* Warm Off-White */
--corporate-gray-200: #EEEEEE;    /* Light Warm Gray */
--corporate-gray-300: #D4D4D4;    /* Medium Light Gray */
--corporate-gray-400: #A3A3A3;    /* Medium Gray */
--corporate-gray-500: #737373;    /* Neutral Gray */
--corporate-gray-600: #525252;    /* Dark Gray */
--corporate-gray-700: #404040;    /* Charcoal */
--corporate-gray-800: #262626;    /* Deep Charcoal */
--corporate-gray-900: #171717;    /* Near Black */
```

### Status Colors
```css
--status-success: #059669;        /* Emerald Green */
--status-warning: #D97706;        /* Warm Amber */
--status-error: #DC2626;          /* Sophisticated Red */
--status-info: #2563EB;           /* Professional Blue */
```

### InfoBlox Service Colors
```css
--infoblox-network: #7C3AED;      /* Royal Purple */
--infoblox-dns: #0891B2;          /* Ocean Blue */
--infoblox-dhcp: #65A30D;         /* Forest Green */
--infoblox-grid: #EA580C;         /* Warm Orange */
--infoblox-ipam: #BE185D;         /* Rose */
```

## 📝 Typography

### Font Families
- **Display Font**: Playfair Display (elegant serif for headings)
- **Primary Font**: Source Sans Pro (clean sans-serif for body text)
- **Monospace Font**: JetBrains Mono (technical data and code)

### Typography Scale
```css
.marriott-font-display {
  font-family: 'Playfair Display', Georgia, serif;
  font-weight: 500;
  letter-spacing: -0.025em;
}

.marriott-font-primary {
  font-family: 'Source Sans Pro', sans-serif;
  font-weight: 400;
  letter-spacing: -0.01em;
}

.marriott-font-mono {
  font-family: 'JetBrains Mono', monospace;
  font-weight: 400;
}
```

## 🧩 Component Library

### Buttons

#### Primary Button
```html
<button class="marriott-button">Primary Action</button>
```
- Deep burgundy background with gold hover effect
- Uppercase text with letter spacing
- Subtle shadow and hover animations

#### Secondary Button
```html
<button class="marriott-button-secondary">Secondary Action</button>
```
- Transparent background with burgundy border
- Fills with burgundy on hover

#### Gold Button
```html
<button class="marriott-button-gold">Premium Action</button>
```
- Marriott gold background
- Used for premium or special actions

### Cards

#### Standard Card
```html
<div class="marriott-card">Content</div>
```
- Clean white background with subtle shadow
- Hover effects with gentle lift animation

#### Luxury Card
```html
<div class="marriott-card-luxury">Premium Content</div>
```
- Enhanced shadows and gradients
- Colored top border accent
- Premium feel for important content

#### InfoBlox Service Cards
```html
<div class="marriott-card-luxury infoblox-network-card">Network Content</div>
<div class="marriott-card-luxury infoblox-dns-card">DNS Content</div>
<div class="marriott-card-luxury infoblox-dhcp-card">DHCP Content</div>
<div class="marriott-card-luxury infoblox-grid-card">Grid Content</div>
<div class="marriott-card-luxury infoblox-ipam-card">IPAM Content</div>
```

### Tables

#### Elegant Data Tables
```html
<table class="marriott-table">
  <thead>
    <tr>
      <th>Column Header</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>Data Cell</td>
    </tr>
  </tbody>
</table>
```
- Burgundy header with white text
- Hover effects on rows
- Clean, professional styling

### Forms

#### Input Fields
```html
<div class="marriott-form-group">
  <label class="marriott-label">Field Label</label>
  <input class="marriott-input" placeholder="Enter value...">
</div>
```
- Sophisticated focus states
- Subtle shadows and animations
- Professional appearance

### Progress Indicators

#### Network Utilization Bars
```html
<div class="marriott-progress">
  <div class="marriott-progress-bar marriott-progress-network" style="width: 75%"></div>
</div>
```
- Service-specific color coding
- Animated shimmer effects
- Elegant gradient fills

### Status Indicators

#### Service Status Badges
```html
<span class="marriott-status-indicator marriott-status-online">Online</span>
<span class="marriott-status-indicator marriott-status-offline">Offline</span>
<span class="marriott-status-indicator marriott-status-warning">Warning</span>
<span class="marriott-status-indicator marriott-status-info">Info</span>
```

### Alerts & Notifications

#### Elegant Alert Messages
```html
<div class="marriott-alert marriott-alert-success">Success message</div>
<div class="marriott-alert marriott-alert-warning">Warning message</div>
<div class="marriott-alert marriott-alert-error">Error message</div>
<div class="marriott-alert marriott-alert-info">Info message</div>
```

## 🏗️ Layout Components

### Main Layout
```svelte
<MarriottLayout title="Page Title" currentPage="dashboard">
  <!-- Page content -->
</MarriottLayout>
```

### Dashboard Widgets
```svelte
<MarriottDashboard 
  title="InfoBlox Network Management"
  subtitle="Marriott Corporate Dashboard"
/>
```

### Chat Interface
```svelte
<MarriottChat 
  title="InfoBlox Assistant"
  subtitle="Ask me anything about your network infrastructure"
/>
```

## 🎭 Animation & Interactions

### Entrance Animations
```css
.marriott-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.marriott-slide-up {
  animation: slideUp 0.3s ease-out;
}
```

### Hover Effects
- Subtle lift animations on cards
- Color transitions on buttons
- Shadow depth changes
- Scale transformations

### Loading States
```html
<div class="marriott-loading"></div>
<div class="marriott-skeleton h-4 w-full"></div>
```

## 📱 Responsive Design

### Breakpoints
- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

### Mobile Adaptations
- Collapsible sidebar navigation
- Stacked card layouts
- Simplified table views
- Touch-friendly button sizes

## 🎨 Design Tokens

### Shadows
```css
--shadow-sm: 0 1px 2px 0 rgba(139, 21, 56, 0.05);
--shadow-md: 0 4px 6px -1px rgba(139, 21, 56, 0.1);
--shadow-lg: 0 10px 15px -3px rgba(139, 21, 56, 0.1);
--shadow-xl: 0 20px 25px -5px rgba(139, 21, 56, 0.1);
--shadow-2xl: 0 25px 50px -12px rgba(139, 21, 56, 0.25);
```

### Gradients
```css
--gradient-primary: linear-gradient(135deg, #8B1538 0%, #A91D47 100%);
--gradient-secondary: linear-gradient(135deg, #B8860B 0%, #DAA520 100%);
--gradient-hero: linear-gradient(135deg, #8B1538 0%, #2C5F41 100%);
--gradient-card: linear-gradient(145deg, #FFFFFF 0%, #F7F7F7 100%);
```

## 🔧 Implementation Guidelines

### CSS Class Naming
- Use `marriott-` prefix for all design system classes
- Follow BEM methodology for component variants
- Maintain consistency with existing patterns

### Component Usage
- Always use design system components over custom styling
- Extend components through props rather than CSS overrides
- Maintain accessibility standards

### Color Usage
- Primary burgundy for main actions and branding
- Gold for premium features and highlights
- Forest green for success states and nature themes
- Charcoal for sophisticated text and borders

### Typography Guidelines
- Use Playfair Display for page titles and important headings
- Use Source Sans Pro for all body text and UI elements
- Use JetBrains Mono for code, IPs, and technical data
- Maintain proper contrast ratios for accessibility

## 🎯 Brand Application

### InfoBlox Context
- Network cards use purple accent
- DNS elements use ocean blue
- DHCP components use forest green
- Grid management uses warm orange
- IPAM features use rose accent

### Marriott Hospitality Touch
- Warm, welcoming color palette
- Sophisticated typography choices
- Premium material design elements
- Attention to detail in micro-interactions
- Professional yet approachable interface

This design system creates a cohesive, luxury experience that reflects Marriott's brand values while providing a powerful, professional interface for InfoBlox DDI management.
