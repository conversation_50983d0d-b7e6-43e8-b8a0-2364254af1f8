# Marriott InfoBlox WebUI Dockerfile
FROM node:18-alpine AS frontend-builder

WORKDIR /app

# Copy package files (create minimal ones if they don't exist)
COPY package*.json ./
RUN npm init -y 2>/dev/null || true
RUN npm install express cors helmet morgan dotenv

# Python backend stage
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
RUN pip install fastapi uvicorn sqlalchemy psycopg2-binary redis python-jose[cryptography] passlib[bcrypt] python-multipart

# Copy application files
COPY . .

# Create a simple FastAPI app for testing
RUN echo 'from fastapi import FastAPI; app = FastAPI(); @app.get("/health"): async def health(): return {"status": "healthy"}; @app.get("/"): async def root(): return {"message": "Marriott InfoBlox WebUI", "status": "running"}' > main.py

# Set environment variables
ENV PYTHONPATH=/app
ENV PORT=8080

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Start command
CMD ["python", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8080"]
