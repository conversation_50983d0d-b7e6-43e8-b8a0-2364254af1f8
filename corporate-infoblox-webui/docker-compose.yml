version: '3.8'

services:
  corporate-webui:
    build:
      context: .
      dockerfile: docker/Dockerfile.corporate
    ports:
      - "3000:8080"
    environment:
      - CORPORATE_THEME=enabled
      - ENABLE_INFOBLOX=true
      - INFOBLOX_MCP_SERVER_PATH=python3 /app/infoblox-mcp-server.py
      - DATABASE_URL=***********************************/corporate_webui
      - ENABLE_AUDIT_LOGGING=true
      - ENABLE_RBAC=true
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./infoblox-mcp-server.py:/app/infoblox-mcp-server.py
    depends_on:
      - db
    restart: unless-stopped

  db:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=corporate_webui
      - POSTGRES_USER=webui
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    restart: unless-stopped

volumes:
  postgres_data:
